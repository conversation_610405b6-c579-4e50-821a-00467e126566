{"name": "njsprojectmanagement-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "dev:tenant1": "vite --config vite.config.tenant1.ts", "dev:tenant2": "vite --config vite.config.tenant2.ts", "build": "tsc -b && vite build", "build:tenant1": "tsc -b && vite build --config vite.config.tenant1.ts", "build:tenant2": "tsc -b && vite build --config vite.config.tenant2.ts", "lint": "eslint .", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^6.4.11", "@mui/material": "^6.4.11", "@mui/x-date-pickers": "^7.22.2", "axios": "^1.7.7", "date-fns": "^2.30.0", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.1", "recharts": "^2.15.1", "uuid": "^11.0.3", "wx-react-gantt": "^1.3.1", "yup": "^1.6.1", "zod": "^3.25.41"}, "devDependencies": {"@eslint/js": "^9.11.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "jsdom": "^26.0.0", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.19", "vitest": "^3.0.9"}}